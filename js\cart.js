/**
 * Unique Smart Collection E-commerce
 * Shopping Cart Functionality
 */

// Initialize cart from localStorage or create empty cart
let cart = JSON.parse(localStorage.getItem('uniqueSmartCart')) || [];

// Function to add a product to the cart
function addToCart(productId, quantity) {
    const product = getProductById(productId);

    if (!product) {
        console.error('Product not found');
        return;
    }

    // Check if product is already in cart
    const existingItemIndex = cart.findIndex(item => item.id === productId);

    if (existingItemIndex !== -1) {
        // Update quantity if product already exists in cart
        cart[existingItemIndex].quantity += quantity;
    } else {
        // Add new item to cart
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: quantity
        });
    }

    // Save cart to localStorage
    saveCart();

    // Update cart UI
    updateCartUI();

    // Show success message
    showNotification(`${product.name} added to cart!`, 'success');
}

// Function to remove a product from the cart
function removeFromCart(productId) {
    const productIndex = cart.findIndex(item => item.id === productId);

    if (productIndex !== -1) {
        const product = cart[productIndex];
        cart.splice(productIndex, 1);

        // Save cart to localStorage
        saveCart();

        // Update cart UI
        updateCartUI();

        // Show success message
        showNotification(`${product.name} removed from cart!`, 'info');
    }
}

// Function to update product quantity in cart
function updateCartItemQuantity(productId, quantity) {
    const productIndex = cart.findIndex(item => item.id === productId);

    if (productIndex !== -1) {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            cart[productIndex].quantity = quantity;

            // Save cart to localStorage
            saveCart();

            // Update cart UI
            updateCartUI();
        }
    }
}

// Function to clear the entire cart
function clearCart() {
    cart = [];

    // Save cart to localStorage
    saveCart();

    // Update cart UI
    updateCartUI();

    // Show success message
    showNotification('Cart has been cleared!', 'info');
}

// Function to save cart to localStorage
function saveCart() {
    localStorage.setItem('uniqueSmartCart', JSON.stringify(cart));
    updateCartCount();
}

// Function to update cart count in header
function updateCartCount() {
    const cartCountElements = document.querySelectorAll('.cart-count');
    const itemCount = cart.reduce((total, item) => total + item.quantity, 0);

    cartCountElements.forEach(element => {
        element.textContent = itemCount;
    });
}

// Function to calculate cart totals
function calculateCartTotals() {
    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    // Free shipping for orders over Ksh 10,000 (which is 100 in our base price)
    const shipping = subtotal > 0 ? (subtotal >= 100 ? 0 : 10) : 0;
    const discount = 0; // Can be updated based on coupon logic
    const total = subtotal + shipping - discount;

    return {
        subtotal,
        shipping,
        discount,
        total
    };
}

// Function to update the cart UI
function updateCartUI() {
    const cartItemsList = document.getElementById('cart-items-list');
    const cartEmpty = document.getElementById('cart-empty');
    const cartItems = document.getElementById('cart-items');
    const cartSummary = document.getElementById('cart-summary');

    if (!cartItemsList) return; // Not on cart page

    if (cart.length === 0) {
        // Show empty cart message
        cartEmpty.style.display = 'block';
        cartItems.style.display = 'none';
        cartSummary.style.display = 'none';
    } else {
        // Hide empty cart message and show cart items
        cartEmpty.style.display = 'none';
        cartItems.style.display = 'block';
        cartSummary.style.display = 'block';

        // Populate cart items
        cartItemsList.innerHTML = cart.map(item => `
            <div class="cart-item">
                <div class="product-col">
                    <img src="${item.image}" alt="${item.name}">
                    <div class="product-info">
                        <h4>${item.name}</h4>
                        <button class="remove-item" data-product-id="${item.id}">Remove</button>
                    </div>
                </div>
                <div class="price-col">Ksh ${(item.price * 100).toFixed(0)}</div>
                <div class="quantity-col">
                    <div class="quantity-control">
                        <button class="quantity-btn decrease" data-product-id="${item.id}">-</button>
                        <input type="number" value="${item.quantity}" min="1" data-product-id="${item.id}" class="quantity-input">
                        <button class="quantity-btn increase" data-product-id="${item.id}">+</button>
                    </div>
                </div>
                <div class="subtotal-col">Ksh ${(item.price * item.quantity * 100).toFixed(0)}</div>
                <div class="remove-col">
                    <button class="remove-item-icon" data-product-id="${item.id}"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `).join('');

        // Update cart totals
        const totals = calculateCartTotals();
        document.getElementById('cart-subtotal').textContent = `Ksh ${(totals.subtotal * 100).toFixed(0)}`;
        document.getElementById('cart-shipping').textContent = totals.shipping === 0 ? 'Free' : `Ksh ${(totals.shipping * 100).toFixed(0)}`;

        if (totals.discount > 0) {
            document.getElementById('discount-row').style.display = 'flex';
            document.getElementById('cart-discount').textContent = `-Ksh ${(totals.discount * 100).toFixed(0)}`;
        } else {
            document.getElementById('discount-row').style.display = 'none';
        }

        document.getElementById('cart-total').textContent = `Ksh ${(totals.total * 100).toFixed(0)}`;

        // Add event listeners to cart item buttons
        addCartItemEventListeners();
    }

    // Update cart count in header
    updateCartCount();
}

// Function to add event listeners to cart item buttons
function addCartItemEventListeners() {
    // Remove item buttons
    document.querySelectorAll('.remove-item, .remove-item-icon').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-product-id'));
            removeFromCart(productId);
        });
    });

    // Quantity decrease buttons
    document.querySelectorAll('.quantity-btn.decrease').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-product-id'));
            const currentItem = cart.find(item => item.id === productId);
            if (currentItem) {
                updateCartItemQuantity(productId, currentItem.quantity - 1);
            }
        });
    });

    // Quantity increase buttons
    document.querySelectorAll('.quantity-btn.increase').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-product-id'));
            const currentItem = cart.find(item => item.id === productId);
            if (currentItem) {
                updateCartItemQuantity(productId, currentItem.quantity + 1);
            }
        });
    });

    // Quantity input fields
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const productId = parseInt(this.getAttribute('data-product-id'));
            const quantity = parseInt(this.value);
            if (!isNaN(quantity) && quantity >= 0) {
                updateCartItemQuantity(productId, quantity);
            }
        });
    });

    // Clear cart button
    const clearCartButton = document.getElementById('clear-cart');
    if (clearCartButton) {
        clearCartButton.addEventListener('click', clearCart);
    }
}

// Function to show notification
function showNotification(message, type = 'success') {
    // Create notification element if it doesn't exist
    let notification = document.querySelector('.notification');

    if (!notification) {
        notification = document.createElement('div');
        notification.className = 'notification';
        document.body.appendChild(notification);
    }

    // Set notification content and type
    notification.textContent = message;
    notification.className = `notification ${type}`;

    // Show notification
    notification.classList.add('show');

    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();

    // Initialize cart UI if on cart page
    if (document.getElementById('cart-items-list')) {
        updateCartUI();
    }
});
