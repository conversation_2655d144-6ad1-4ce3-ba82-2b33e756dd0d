/*
 * Unique Smart Collection E-commerce Website
 * Main Stylesheet
 */

/* ===== VARIABLES ===== */
:root {
    /* Primary color palette */
    --primary-color: #2e4053;       /* Deep blue-gray */
    --secondary-color: #e67e22;     /* Vibrant orange */
    --background-color: #f8f9fa;    /* Light background */
    --text-color: #333333;          /* Dark text */
    --light-text: #ffffff;          /* Light text */
    --border-color: #e1e1e1;        /* Light border */
    --success-color: #4caf50;       /* Green for success messages */
    --error-color: #f44336;         /* Red for error messages */
    --warning-color: #ff9800;       /* Orange for warnings */
    --info-color: #2196f3;          /* Blue for info */

    /* Typography */
    --heading-font: 'Playfair Display', serif;
    --body-font: 'Raleway', sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Border radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;

    /* Box shadow */
    --box-shadow-light: 0 2px 5px rgba(0, 0, 0, 0.1);
    --box-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --box-shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    font-weight: 600;
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.6;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

ul, ol {
    list-style: none;
}

/* ===== UTILITY CLASSES ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
    padding-bottom: var(--spacing-md);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
}

.btn:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-medium);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.btn-full {
    width: 100%;
}

.btn-link {
    background: none;
    color: var(--primary-color);
    padding: 0;
    font-weight: 600;
    text-decoration: underline;
    text-transform: none;
    letter-spacing: normal;
}

.btn-link:hover {
    color: var(--secondary-color);
    background: none;
    transform: none;
    box-shadow: none;
}

/* ===== HEADER ===== */
header {
    background-color: #fff;
    box-shadow: var(--box-shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-md);
}

.logo {
    display: flex;
    align-items: center;
}

.logo h1 {
    font-size: 1.75rem;
    margin-bottom: 0;
    color: var(--primary-color);
    font-weight: 800;
    letter-spacing: 1px;
}

.logo img {
    height: 40px;
    margin-right: var(--spacing-sm);
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: var(--spacing-lg);
}

nav ul li a {
    font-weight: 600;
    position: relative;
    padding-bottom: 5px;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: width var(--transition-medium);
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

.header-icons {
    display: flex;
    align-items: center;
}

.header-icons a {
    margin-left: var(--spacing-md);
    font-size: 1.25rem;
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--secondary-color);
    color: var(--light-text);
    font-size: 0.75rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh; /* Full viewport height */
    width: 100%; /* Full width */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
    text-align: center;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3));
    z-index: 1;
}

.hero-slideshow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #000;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 1;
    z-index: 0;
}

.slide-1 {
    background-image: url('https://images.unsplash.com/photo-1615874959474-d609969a20ed?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'); /* Luxury Bedding */
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    position: relative;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    animation: fadeIn 1.5s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-content .btn {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    letter-spacing: 1.5px;
    background-color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-content .btn:hover {
    background-color: transparent;
    color: var(--light-text);
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.hero-content h2 {
    font-size: 3.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: 800;
    letter-spacing: 1px;
}

.hero-content p {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-lg);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* ===== FEATURED CATEGORIES ===== */
.featured-categories {
    padding: var(--spacing-xl) 0;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.category-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
    text-align: center;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-medium);
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card h3 {
    margin: var(--spacing-md) 0;
    font-size: 1.25rem;
}

.category-card .btn-small {
    margin-bottom: var(--spacing-md);
}

/* ===== FEATURED PRODUCTS ===== */
.featured-products {
    padding: var(--spacing-xl) 0;
    background-color: #fff;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.product-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-medium);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background-color: var(--secondary-color);
    color: var(--light-text);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
}

.product-actions {
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding: var(--spacing-sm);
    background-color: rgba(255, 255, 255, 0.9);
    transition: bottom var(--transition-medium);
}

.product-card:hover .product-actions {
    bottom: 0;
}

.product-actions button {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--primary-color);
    margin: 0 var(--spacing-sm);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.product-actions button:hover {
    color: var(--secondary-color);
}

.product-info {
    padding: var(--spacing-md);
    text-align: center;
}

.product-category {
    font-size: 0.875rem;
    color: #777;
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.product-title {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-price {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.old-price {
    font-size: 0.875rem;
    color: #999;
    text-decoration: line-through;
    margin-left: var(--spacing-sm);
}

.product-rating {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.product-rating i {
    color: #ffc107;
    font-size: 0.875rem;
    margin: 0 1px;
}

.product-rating .count {
    font-size: 0.75rem;
    color: #777;
    margin-left: var(--spacing-xs);
}

/* ===== GALLERY PAGE ===== */
.gallery-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-color);
}

.gallery-categories {
    margin-bottom: var(--spacing-xl);
}

.gallery-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-xl);
}

/* Price Tag Overlay */
.price-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10;
    transform: rotate(-5deg);
    border: 2px solid #fff;
}

.price-tag::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, #ffd700, #ffb347);
    border-radius: 25px;
    z-index: -1;
}

.category-card {
    position: relative;
}

/* WhatsApp Button */
.whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    padding: 10px 16px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

.whatsapp-btn i {
    font-size: 1.1rem;
}

/* ===== CATEGORIES PAGE ===== */
.categories-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-color);
}

.main-categories {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-bottom: var(--spacing-sm);
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.category-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    padding-bottom: var(--spacing-md);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-fast);
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card h3 {
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: 0;
    font-size: 1.1rem;
    text-align: center;
}

.category-card .btn-small {
    display: block;
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: var(--light-text);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.category-card .btn-small:hover {
    background-color: var(--secondary-color);
}

.sub-categories {
    margin-bottom: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

/* ===== PROMO BANNER ===== */
.promo-banner {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--light-text);
    text-align: center;
}

.promo-content {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.promo-content h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
}

.promo-content p {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
}

.promo-content p span {
    font-weight: 700;
    color: var(--secondary-color);
    font-size: 1.5rem;
}

/* ===== NEWSLETTER ===== */
.newsletter {
    padding: var(--spacing-xl) 0;
    background-color: var(--primary-color);
    color: var(--light-text);
}

.newsletter-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.newsletter-content h2 {
    color: var(--light-text);
    margin-bottom: var(--spacing-sm);
}

.newsletter-content p {
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    font-family: var(--body-font);
}

.newsletter-form .btn {
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    background-color: var(--secondary-color);
}

.newsletter-form .btn:hover {
    background-color: #b38a5e;
}

/* ===== FOOTER ===== */
footer {
    background-color: #f8f9fa;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.footer-column h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-column p {
    margin-bottom: var(--spacing-md);
    color: #666;
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-column ul li a {
    color: #666;
    transition: color var(--transition-fast), transform var(--transition-fast);
    display: inline-block;
}

.footer-column ul li a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.social-icons {
    display: flex;
    gap: var(--spacing-sm);
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #eee;
    border-radius: 50%;
    color: var(--primary-color);
    transition: all var(--transition-fast);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-3px);
}

.contact-info li {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    color: #666;
}

.contact-info li i {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
    margin-top: 5px;
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
}

.payment-methods {
    display: flex;
    gap: var(--spacing-sm);
}

.payment-methods i {
    font-size: 1.5rem;
    color: #666;
    transition: color var(--transition-fast);
}

.payment-methods i:hover {
    color: var(--primary-color);
}

/* ===== LOGO STYLING ===== */
.logo h1 {
    font-family: var(--heading-font);
    font-weight: 800;
    letter-spacing: 1px;
}

.logo .enterprise {
    font-weight: 400;
    color: var(--secondary-color);
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: 0.875rem;
}

.breadcrumb a {
    color: var(--primary-color);
}

.breadcrumb span {
    color: #777;
}

.breadcrumb a:hover {
    color: var(--secondary-color);
}

.breadcrumb a::after {
    content: '/';
    margin: 0 var(--spacing-sm);
    color: #ccc;
}

/* ===== PAGE HEADER ===== */
.page-header {
    background-color: #f8f9fa;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.page-header h1 {
    margin-bottom: var(--spacing-xs);
}

/* ===== CONTACT PAGE ===== */
.contact-section {
    padding: var(--spacing-xl) 0;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.contact-info {
    background-color: #fff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
}

.contact-info h2 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.contact-info p {
    margin-bottom: var(--spacing-lg);
    color: #666;
}

.info-item {
    display: flex;
    margin-bottom: var(--spacing-lg);
}

.info-item i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: var(--spacing-md);
    margin-top: 5px;
}

.info-item h3 {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.info-item p {
    margin-bottom: var(--spacing-xs);
    color: #666;
}

.social-media h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.contact-form-container {
    background-color: #fff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
}

.contact-form-container h2 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--primary-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-family: var(--body-font);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
}

.map-section {
    padding: var(--spacing-xl) 0;
    background-color: #f8f9fa;
}

.map-container {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
}

.faq-section {
    padding: var(--spacing-xl) 0;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--box-shadow-light);
    overflow: hidden;
}

.faq-question {
    padding: var(--spacing-md);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color var(--transition-fast);
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.faq-question h3 {
    margin-bottom: 0;
    font-size: 1.1rem;
}

.faq-question i {
    color: var(--secondary-color);
    transition: transform var(--transition-fast);
}

.faq-question i.rotate {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 var(--spacing-md) var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* ===== ABOUT PAGE ===== */
.about-section {
    padding: var(--spacing-xl) 0 var(--spacing-md) 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

.about-images {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    height: fit-content;
}

.about-image-main {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.about-image-main img {
    width: 100%;
    height: auto;
    object-fit: contain;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-medium);
    transition: transform 0.5s ease;
    margin-top: var(--spacing-sm);
}

.about-image-secondary {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-image-secondary img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-medium);
    transition: transform 0.5s ease;
}

.about-image-main::after,
.about-image-secondary::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius-md);
    opacity: 0;
    transform: scale(1.1);
    transition: all 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.about-image-main:hover::after,
.about-image-secondary:hover::after {
    opacity: 0.5;
    transform: scale(1);
}

.about-image-main:hover img,
.about-image-secondary:hover img {
    transform: scale(1.05);
}

.about-text h2 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.about-text p {
    margin-bottom: var(--spacing-md);
    color: #666;
    line-height: 1.8;
}

.about-text .welcome-points {
    background-color: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--secondary-color);
    font-size: 1.05rem;
    line-height: 2;
}

.about-text .tagline {
    font-size: 1.2rem;
    color: var(--primary-color);
    text-align: center;
    margin-top: var(--spacing-lg);
}

.who-we-are-section {
    padding: var(--spacing-md) 0 var(--spacing-xl) 0;
    background-color: #f8f9fa;
}

/* Home Decor Section */
.home-decor-section {
    padding: var(--spacing-xl) 0;
    background-color: #fff;
}

.section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--spacing-xl);
    color: #666;
    line-height: 1.8;
}

.home-decor-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.decor-item {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.decor-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-medium);
}

.decor-image {
    height: 200px;
    overflow: hidden;
}

.decor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.decor-item:hover .decor-image img {
    transform: scale(1.05);
}

.decor-info {
    padding: var(--spacing-md);
}

.decor-info h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.decor-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.decor-cta {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.who-we-are-content {
    max-width: 1000px;
    margin: 0 auto;
}

.who-we-are-text h2 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.who-we-are-text h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.who-we-are-text h3 {
    margin: var(--spacing-md) 0;
    color: var(--primary-color);
}

.specialties {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.specialty-item {
    background-color: #fff;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    text-align: center;
    overflow: hidden;
}

.specialty-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-medium);
}

.specialty-image {
    width: 100%;
    height: 120px;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.specialty-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
    border-radius: var(--border-radius-sm);
}

.specialty-item:hover .specialty-image img {
    transform: scale(1.1);
}

.specialty-item i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
    position: relative;
    z-index: 2;
}

.specialty-item h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1rem;
}

.mission-section {
    padding: var(--spacing-xl) 0;
    background-color: #f8f9fa;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.mission-image {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mission-image img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-medium);
    transition: transform 0.5s ease;
}

.mission-image::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius-md);
    opacity: 0;
    transform: scale(1.1);
    transition: all 0.5s ease;
    pointer-events: none;
    z-index: 1;
}

.mission-image:hover::after {
    opacity: 0.5;
    transform: scale(1);
}

.mission-image:hover img {
    transform: scale(1.05);
}

.mission-text h2 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.mission-text h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.mission-text p {
    margin-bottom: var(--spacing-lg);
    color: #666;
    line-height: 1.8;
}

.mission-vision {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.mission-block, .vision-block {
    flex: 1;
    background-color: #fff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
}

.mission-block h3, .vision-block h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.values {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.value-item {
    background-color: #fff;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
    text-align: center;
}

.value-item i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.value-item h3, .value-item h4 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.value-item p {
    margin-bottom: 0;
    color: #666;
    font-size: 0.9rem;
}

.why-choose-section {
    padding: var(--spacing-xl) 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.feature-card {
    background-color: #fff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
    text-align: center;
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-medium);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: var(--light-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.feature-icon i {
    font-size: 1.75rem;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.feature-card p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.team-section {
    padding: var(--spacing-xl) 0;
    background-color: #f8f9fa;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.team-member {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--box-shadow-light);
    text-align: center;
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-medium);
}

.member-image {
    height: 250px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.team-member h3 {
    margin: var(--spacing-md) var(--spacing-md) var(--spacing-xs);
    font-size: 1.2rem;
    color: var(--primary-color);
}

.member-role {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.team-member p:not(.member-role) {
    padding: 0 var(--spacing-md) var(--spacing-md);
    color: #666;
    font-size: 0.9rem;
}

.testimonials-section {
    padding: var(--spacing-xl) 0;
    background-color: #f8f9fa;
    position: relative;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2029&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 0;
}

.testimonials-section .container {
    position: relative;
    z-index: 1;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.testimonial {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-light);
    padding: var(--spacing-lg);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.testimonial::before {
    content: '\201C';
    font-family: Georgia, serif;
    position: absolute;
    top: -15px;
    left: 10px;
    font-size: 120px;
    color: var(--secondary-color);
    opacity: 0.1;
    z-index: 0;
}

.testimonial:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-medium);
}

.testimonial-content {
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 1;
}

.testimonial-content p {
    color: #666;
    font-style: italic;
    line-height: 1.8;
    position: relative;
    padding: 0 var(--spacing-md);
}

.testimonial-author {
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    padding-top: var(--spacing-md);
}

.author-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-md);
    border: 3px solid var(--secondary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.testimonial:hover .author-image img {
    transform: scale(1.1);
}

.author-info h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
    font-weight: 600;
}

.rating {
    color: #ffc107;
}

.cta-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--light-text);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.cta-content .btn {
    background-color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

.cta-content .btn:hover {
    background-color: transparent;
    color: var(--light-text);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-up {
    animation: slideUp 0.5s ease forwards;
}
