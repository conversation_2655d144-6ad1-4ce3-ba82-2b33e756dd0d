/**
 * Unique Smart Collection E-commerce
 * Products Data and Display Logic
 */

// Sample product data
const products = [
    {
        id: 1,
        name: "Baby Clothes Set",
        category: "Clothing",
        price: 50.00, // Kshs 5000
        oldPrice: 65.00,
        image: "https://images.unsplash.com/photo-1522771930-78848d9293e8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80",
        rating: 4.9,
        reviewCount: 156,
        isNew: true,
        isFeatured: true,
        description: "High-quality baby clothes set including tops, bottoms, and accessories. Made from soft, comfortable fabric that's gentle on your baby's skin."
    },
    {
        id: 2,
        name: "Plush Teddy Bear",
        category: "Toys",
        price: 25.00, // Ksh 2,500
        oldPrice: 35.00,
        image: "https://images.unsplash.com/photo-1562040506-a9b32cb51b94?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.8,
        reviewCount: 98,
        isNew: false,
        isFeatured: true,
        description: "Soft and cuddly teddy bear made from premium plush material. Perfect companion for children of all ages. Safe and durable."
    },
    {
        id: 3,
        name: "Men's Cotton T-Shirt",
        category: "Clothing",
        price: 20.00, // Ksh 2,000
        oldPrice: null,
        image: "https://images.unsplash.com/photo-1581655353564-df123a1eb820?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
        rating: 4.5,
        reviewCount: 87,
        isNew: true,
        isFeatured: true,
        description: "Made from 100% cotton, this comfortable t-shirt is both stylish and breathable. Available in multiple colors and sizes."
    },
    {
        id: 4,
        name: "Wooden Building Blocks",
        category: "Toys",
        price: 35.00, // Ksh 3,500
        oldPrice: 45.00,
        image: "https://images.unsplash.com/photo-1587654780291-39c9404d746b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.7,
        reviewCount: 65,
        isNew: false,
        isFeatured: true,
        description: "Set of 50 wooden building blocks in various shapes and colors. Helps develop fine motor skills and creativity. Made from sustainable wood."
    },
    {
        id: 5,
        name: "Decorative Throw Pillows",
        category: "Decorations",
        price: 30.00, // Ksh 3,000
        oldPrice: 40.00,
        image: "https://images.unsplash.com/photo-1540730930991-a9286a5f5020?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.6,
        reviewCount: 78,
        isNew: false,
        isFeatured: true,
        description: "Set of 2 decorative throw pillows with removable covers. Perfect for adding a touch of style to your living room or bedroom."
    },
    {
        id: 6,
        name: "Women's Summer Dress",
        category: "Clothing",
        price: 45.00, // Ksh 4,500
        oldPrice: null,
        image: "https://images.unsplash.com/photo-1623609163859-ca93c959b5b8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
        rating: 4.8,
        reviewCount: 143,
        isNew: true,
        isFeatured: true,
        description: "Elegant summer dress made from lightweight, breathable fabric. Features a flattering cut and comfortable fit. Available in various patterns."
    },
    {
        id: 7,
        name: "Wall Art Canvas Print",
        category: "Decorations",
        price: 55.00, // Ksh 5,500
        oldPrice: 70.00,
        image: "https://images.unsplash.com/photo-1513519245088-0e12902e5a38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
        rating: 4.4,
        reviewCount: 65,
        isNew: false,
        isFeatured: true,
        description: "Beautiful canvas print wall art to enhance your home decor. High-quality printing on premium canvas with wooden frame. Ready to hang."
    },
    {
        id: 8,
        name: "Remote Control Car",
        category: "Toys",
        price: 40.00, // Ksh 4,000
        oldPrice: 50.00,
        image: "https://images.unsplash.com/photo-1594787318286-3d835c1d207f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.3,
        reviewCount: 92,
        isNew: false,
        isFeatured: true,
        description: "Fast and durable remote control car with rechargeable battery. Features responsive controls and rugged design for indoor and outdoor play."
    },
    {
        id: 9,
        name: "Luxury Duvet",
        category: "Duvet",
        price: 120.00, // Ksh 12,000
        oldPrice: 150.00,
        image: "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80",
        rating: 4.8,
        reviewCount: 112,
        isNew: true,
        isFeatured: true,
        description: "Premium quality duvet with soft microfiber filling. Provides warmth and comfort for a good night's sleep. Machine washable and hypoallergenic."
    },
    {
        id: 10,
        name: "Cotton Bedsheets Set",
        category: "Bedsheets",
        price: 80.00, // Ksh 8,000
        oldPrice: 95.00,
        image: "https://images.unsplash.com/photo-1584100936595-c0654b55a2e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80",
        rating: 4.7,
        reviewCount: 98,
        isNew: false,
        isFeatured: true,
        description: "100% cotton bedsheet set including fitted sheet, flat sheet, and pillowcases. Soft, breathable, and durable with a 300 thread count for ultimate comfort."
    },
    {
        id: 11,
        name: "Baby Dress",
        category: "Baby dress",
        price: 35.00, // Ksh 3,500
        oldPrice: 45.00,
        image: "https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.9,
        reviewCount: 76,
        isNew: true,
        isFeatured: true,
        description: "Adorable baby dress made from soft, gentle fabric. Perfect for special occasions or everyday wear. Available in various colors and patterns."
    },
    {
        id: 12,
        name: "Decorative String Lights",
        category: "Decorations",
        price: 25.00, // Ksh 2,500
        oldPrice: 35.00,
        image: "https://images.unsplash.com/photo-1545856229-a9ff1909141a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.5,
        reviewCount: 88,
        isNew: true,
        isFeatured: true,
        description: "Warm white LED string lights perfect for creating a cozy atmosphere in any room. Indoor and outdoor use, energy-efficient and long-lasting."
    },
    {
        id: 13,
        name: "King Size Bedding Set",
        category: "Beddings",
        price: 150.00, // Ksh 15,000
        oldPrice: 180.00,
        image: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.9,
        reviewCount: 105,
        isNew: false,
        isFeatured: true,
        description: "Complete king size bedding set including duvet cover, fitted sheet, flat sheet, and 4 pillowcases. Luxurious comfort with elegant design."
    },
    {
        id: 14,
        name: "Educational Board Game",
        category: "Toys",
        price: 30.00, // Ksh 3,000
        oldPrice: null,
        image: "https://images.unsplash.com/photo-1610890716171-6b1bb98ffd09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80",
        rating: 4.6,
        reviewCount: 72,
        isNew: true,
        isFeatured: false,
        description: "Fun and educational board game for the whole family. Develops strategic thinking, problem-solving skills, and provides hours of entertainment."
    },
    {
        id: 15,
        name: "Warm Winter Duvet",
        category: "Duvet",
        price: 140.00, // Ksh 14,000
        oldPrice: 160.00,
        image: "https://images.unsplash.com/photo-1584100936595-c0654b55a2e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80",
        rating: 4.8,
        reviewCount: 94,
        isNew: false,
        isFeatured: false,
        description: "Extra warm winter duvet with high-quality down alternative filling. Perfect for cold nights, providing exceptional warmth without being too heavy."
    }
];

// Function to create a product card HTML
function createProductCard(product) {
    return `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
                ${product.isNew ? '<span class="product-badge">New</span>' : ''}
                <div class="product-actions">
                    <button class="add-to-cart" data-product-id="${product.id}"><i class="fas fa-shopping-cart"></i></button>
                    <button class="add-to-wishlist" data-product-id="${product.id}"><i class="far fa-heart"></i></button>
                    <button class="quick-view" data-product-id="${product.id}"><i class="far fa-eye"></i></button>
                </div>
            </div>
            <div class="product-info">
                <div class="product-category">${product.category}</div>
                <h3 class="product-title">${product.name}</h3>
                <div class="product-price">
                    <span class="current-price">Ksh ${(product.price * 100).toFixed(0)}</span>
                    ${product.oldPrice ? `<span class="old-price">Ksh ${(product.oldPrice * 100).toFixed(0)}</span>` : ''}
                </div>
                <div class="product-rating">
                    ${generateRatingStars(product.rating)}
                    <span class="count">(${product.reviewCount})</span>
                </div>
            </div>
        </div>
    `;
}

// Function to generate rating stars
function generateRatingStars(rating) {
    let stars = '';
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
        if (i <= fullStars) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i === fullStars + 1 && halfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }

    return stars;
}

// Function to display featured products on the homepage
function displayFeaturedProducts() {
    const featuredProductsContainer = document.getElementById('featured-products-container');
    if (!featuredProductsContainer) return;

    const featuredProducts = products.filter(product => product.isFeatured).slice(0, 8);

    featuredProductsContainer.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');

    // Add event listeners to the add to cart buttons
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = parseInt(this.getAttribute('data-product-id'));
            addToCart(productId, 1);
        });
    });
}

// Function to display all products on the products page
function displayAllProducts() {
    const productsContainer = document.getElementById('products-container');
    if (!productsContainer) return;

    // Check if there's a category filter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const categoryFilter = urlParams.get('category');

    let filteredProducts = products;

    // Filter products by category if specified
    if (categoryFilter) {
        // Convert URL parameter to match category format (e.g., 'baby-dress' to 'Baby dress')
        const formattedCategory = categoryFilter
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');

        filteredProducts = products.filter(product =>
            product.category.toLowerCase() === formattedCategory.toLowerCase() ||
            product.category.toLowerCase() === categoryFilter.toLowerCase()
        );

        // Update page title to show category
        document.title = `${formattedCategory} - Unique Smart Collection`;

        // Update header if it exists
        const pageHeader = document.querySelector('.page-header h1');
        if (pageHeader) {
            pageHeader.textContent = formattedCategory;
        }

        // Update breadcrumb if it exists
        const breadcrumbSpan = document.querySelector('.breadcrumb span');
        if (breadcrumbSpan) {
            breadcrumbSpan.textContent = formattedCategory;
        }

        // Check category checkboxes in filter
        const categoryCheckbox = document.getElementById(categoryFilter);
        if (categoryCheckbox) {
            categoryCheckbox.checked = true;
        }
    }

    productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');

    // Update products count
    const productsCount = document.getElementById('products-count');
    if (productsCount) {
        productsCount.textContent = filteredProducts.length;
    }

    // Add event listeners to the add to cart buttons
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = parseInt(this.getAttribute('data-product-id'));
            addToCart(productId, 1);
        });
    });
}

// Function to get product by ID
function getProductById(productId) {
    return products.find(product => product.id === productId);
}

// Function to handle category filters (removed)

// Initialize product displays when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    displayFeaturedProducts();
    displayAllProducts();
});
