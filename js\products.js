/**
 * Unique Smart Collection E-commerce
 * Products Data and Display Logic
 */

// Featured products data - 8 products from different categories with realistic Kenyan prices
const products = [
    {
        id: 1,
        name: "Luxury Duvets",
        category: "Bedding & Home Linen",
        price: 35.00, // Ksh 3,500
        oldPrice: 45.00,
        image: "img/DUVET.jpg",
        rating: 4.9,
        reviewCount: 156,
        isNew: true,
        isFeatured: true,
        description: "Premium quality duvet with soft microfiber filling. Provides warmth and comfort for a good night's sleep. Machine washable and hypoallergenic."
    },
    {
        id: 2,
        name: "Baby Clothing",
        category: "Baby & Kids Essentials",
        price: 12.00, // Ksh 1,200
        oldPrice: 15.00,
        image: "img/baby clothes.jpg",
        rating: 4.8,
        reviewCount: 98,
        isNew: false,
        isFeatured: true,
        description: "Adorable baby clothing made from soft, gentle fabric. Perfect for everyday wear. Available in various colors and sizes."
    },
    {
        id: 3,
        name: "Handbag 2",
        category: "Fashion & Accessories",
        price: 18.00, // Ksh 1,800
        oldPrice: null,
        image: "img/handbag 2.jpeg",
        rating: 4.5,
        reviewCount: 87,
        isNew: true,
        isFeatured: true,
        description: "Stylish handbag perfect for everyday use. Made from quality materials with spacious compartments for all your essentials."
    },
    {
        id: 4,
        name: "Suitcases",
        category: "Travel & Luggage",
        price: 55.00, // Ksh 5,500
        oldPrice: 65.00,
        image: "https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
        rating: 4.7,
        reviewCount: 65,
        isNew: false,
        isFeatured: true,
        description: "Durable and lightweight suitcase perfect for travel. Features smooth-rolling wheels and secure locks for your peace of mind."
    },
    {
        id: 5,
        name: "Artificial Flowers",
        category: "Home & Decor",
        price: 8.00, // Ksh 800
        oldPrice: 12.00,
        image: "img/artificial flowers.jpg",
        rating: 4.6,
        reviewCount: 78,
        isNew: false,
        isFeatured: true,
        description: "Beautiful artificial flowers that look real. Perfect for home decoration and require no maintenance. Long-lasting and vibrant colors."
    },
    {
        id: 6,
        name: "Electric Kettle",
        category: "Electronics & Appliances",
        price: 25.00, // Ksh 2,500
        oldPrice: null,
        image: "img/electric kettle.jpg",
        rating: 4.8,
        reviewCount: 143,
        isNew: true,
        isFeatured: true,
        description: "Fast-boiling electric kettle with automatic shut-off feature. Energy efficient and safe to use. Perfect for tea, coffee, and instant meals."
    },
    {
        id: 7,
        name: "Umbrellas",
        category: "Miscellaneous Essentials",
        price: 6.00, // Ksh 600
        oldPrice: 8.00,
        image: "img/umbrellas 2.jpg",
        rating: 4.4,
        reviewCount: 65,
        isNew: false,
        isFeatured: true,
        description: "Compact and durable umbrella perfect for rainy days. Wind-resistant design with comfortable grip handle. Easy to carry and store."
    },
    {
        id: 8,
        name: "Educational Toys",
        category: "Toys Collection",
        price: 15.00, // Ksh 1,500
        oldPrice: 20.00,
        image: "img/educational toys.jpg",
        rating: 4.3,
        reviewCount: 92,
        isNew: false,
        isFeatured: true,
        description: "Fun and educational toys that help develop children's cognitive skills. Safe, durable, and designed to inspire creativity and learning."
    }
];

// Function to create a product card HTML
function createProductCard(product) {
    return `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
                ${product.isNew ? '<span class="product-badge">New</span>' : ''}
            </div>
            <div class="product-info">
                <div class="product-category">${product.category}</div>
                <h3 class="product-title">${product.name}</h3>
                <div class="product-price">
                    <span class="current-price">Ksh ${(product.price * 100).toFixed(0)}</span>
                    ${product.oldPrice ? `<span class="old-price">Ksh ${(product.oldPrice * 100).toFixed(0)}</span>` : ''}
                </div>
                <div class="product-rating">
                    ${generateRatingStars(product.rating)}
                    <span class="count">(${product.reviewCount})</span>
                </div>
                <a href="https://wa.me/254727217744?text=Hi%20Unique%20Smart%20Collection!%20I%20am%20interested%20in%20${encodeURIComponent(product.name)}.%20Could%20you%20please%20share%20more%20details%20and%20pricing?" class="whatsapp-btn" target="_blank">
                    <i class="fab fa-whatsapp"></i>
                    Ask & Buy
                </a>
            </div>
        </div>
    `;
}

// Function to generate rating stars
function generateRatingStars(rating) {
    let stars = '';
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
        if (i <= fullStars) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i === fullStars + 1 && halfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }

    return stars;
}

// Function to display featured products on the homepage
function displayFeaturedProducts() {
    const featuredProductsContainer = document.getElementById('featured-products-container');
    if (!featuredProductsContainer) return;

    const featuredProducts = products.filter(product => product.isFeatured).slice(0, 8);

    featuredProductsContainer.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');
}

// Function to display all products on the products page
function displayAllProducts() {
    const productsContainer = document.getElementById('products-container');
    if (!productsContainer) return;

    // Check if there's a category filter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const categoryFilter = urlParams.get('category');

    let filteredProducts = products;

    // Filter products by category if specified
    if (categoryFilter) {
        // Convert URL parameter to match category format (e.g., 'baby-dress' to 'Baby dress')
        const formattedCategory = categoryFilter
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');

        filteredProducts = products.filter(product =>
            product.category.toLowerCase() === formattedCategory.toLowerCase() ||
            product.category.toLowerCase() === categoryFilter.toLowerCase()
        );

        // Update page title to show category
        document.title = `${formattedCategory} - Unique Smart Collection`;

        // Update header if it exists
        const pageHeader = document.querySelector('.page-header h1');
        if (pageHeader) {
            pageHeader.textContent = formattedCategory;
        }

        // Update breadcrumb if it exists
        const breadcrumbSpan = document.querySelector('.breadcrumb span');
        if (breadcrumbSpan) {
            breadcrumbSpan.textContent = formattedCategory;
        }

        // Check category checkboxes in filter
        const categoryCheckbox = document.getElementById(categoryFilter);
        if (categoryCheckbox) {
            categoryCheckbox.checked = true;
        }
    }

    productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');

    // Update products count
    const productsCount = document.getElementById('products-count');
    if (productsCount) {
        productsCount.textContent = filteredProducts.length;
    }
}

// Function to get product by ID
function getProductById(productId) {
    return products.find(product => product.id === productId);
}

// Function to handle category filters (removed)

// Initialize product displays when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    displayFeaturedProducts();
    displayAllProducts();
});
