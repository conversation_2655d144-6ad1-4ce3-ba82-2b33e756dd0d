/*
 * Unique Smart Collection E-commerce Website
 * Responsive Stylesheet
 */

/* ===== RESPONSIVE STYLES ===== */

/* Large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }

    .category-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-md);
    }

    .hero-content h2 {
        font-size: 2.5rem;
    }
}

/* Medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }

    header .container {
        flex-wrap: wrap;
    }

    .logo {
        margin-bottom: var(--spacing-sm);
    }

    nav ul li {
        margin-left: var(--spacing-md);
    }

    .category-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .hero {
        height: 500px;
    }

    .hero-content h2 {
        font-size: 2.25rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }
}

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }

    header .container {
        justify-content: space-between;
    }

    .logo {
        margin-bottom: 0;
    }

    nav {
        order: 3;
        width: 100%;
        margin-top: var(--spacing-md);
    }

    nav ul {
        justify-content: space-between;
        width: 100%;
    }

    nav ul li {
        margin-left: 0;
    }

    .hero {
        height: 400px;
    }

    .hero-content {
        padding: var(--spacing-lg);
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .category-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-bottom {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .payment-methods {
        margin-top: var(--spacing-md);
    }

    .checkout-container {
        flex-direction: column;
    }

    .order-summary {
        margin-top: var(--spacing-lg);
        margin-left: 0;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .header-icons a {
        margin-left: var(--spacing-sm);
        font-size: 1.1rem;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 0 var(--spacing-sm) var(--spacing-sm) var(--spacing-sm);
    }

    .hero {
        height: 350px;
    }

    .hero-content {
        padding: var(--spacing-md);
    }

    .hero-content h2 {
        font-size: 1.75rem;
        margin-bottom: var(--spacing-sm);
    }

    .hero-content p {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-md);
    }

    .section-title {
        font-size: 1.5rem;
        margin-bottom: var(--spacing-lg);
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        width: 100%;
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }

    .newsletter-form .btn {
        width: 100%;
    }

    /* Gallery page extra small device styles */
    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .filter-btn {
        padding: 0.4rem 1rem;
        margin: 0 0.3rem 0.8rem 0.3rem;
        font-size: 0.9rem;
    }

    /* Categories page extra small device styles */
    .category-grid {
        grid-template-columns: 1fr;
    }

    .product-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .product-card {
        min-width: auto;
        max-width: 100%;
        margin-bottom: var(--spacing-md);
    }

    .footer-column {
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-column ul {
        text-align: center;
    }

    .contact-info li {
        justify-content: center;
    }

    /* Contact page responsive styles */
    .contact-container {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        text-align: center;
    }

    .info-item i {
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }

    .social-media {
        text-align: center;
    }

    /* Gallery page responsive styles */
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Categories page responsive styles */
    .category-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* About page responsive styles */
    .about-content,
    .mission-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .about-image,
    .mission-image {
        margin-bottom: var(--spacing-md);
        height: 350px;
    }

    .about-image img,
    .mission-image img {
        object-fit: contain;
    }

    .about-image::after,
    .mission-image::after {
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
    }

    .specialties {
        grid-template-columns: 1fr;
    }

    .home-decor-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .mission-vision {
        flex-direction: column;
    }

    .mission-block, .vision-block {
        margin-bottom: var(--spacing-md);
    }

    .values {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .home-decor-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    /* Additional About page styles for extra small devices */
    .about-text .welcome-points {
        padding: var(--spacing-sm);
        font-size: 0.95rem;
        line-height: 1.8;
    }

    .about-text .tagline {
        font-size: 1.1rem;
    }

    .about-image,
    .mission-image {
        height: 250px;
    }

    .testimonial {
        padding: var(--spacing-md);
    }

    .testimonial::before {
        font-size: 80px;
        top: -10px;
        left: 5px;
    }

    .author-image {
        width: 50px;
        height: 50px;
        border-width: 2px;
    }
}
