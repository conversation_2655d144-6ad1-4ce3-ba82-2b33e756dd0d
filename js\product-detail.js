/**
 * Unique Smart Collection E-commerce
 * Product Detail Page Functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get product ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));

    if (!productId) {
        // Redirect to products page if no product ID is provided
        window.location.href = 'products.html';
        return;
    }

    // Get product data
    const product = getProductById(productId);

    if (!product) {
        // Show error message if product is not found
        document.getElementById('product-detail-container').innerHTML = `
            <div class="error-message">
                <h2>Product Not Found</h2>
                <p>The product you are looking for does not exist or has been removed.</p>
                <a href="products.html" class="btn">Back to Products</a>
            </div>
        `;
        return;
    }

    // Update page title and breadcrumb
    document.title = `${product.name} - Unique Smart Collection`;
    document.getElementById('product-breadcrumb').textContent = product.name;

    // Display product details
    document.getElementById('product-detail-container').innerHTML = `
        <div class="product-detail-content">
            <div class="product-gallery">
                <div class="main-image">
                    <img src="${product.image}" alt="${product.name}">
                    ${product.isNew ? '<span class="product-badge">New</span>' : ''}
                </div>
                <div class="thumbnail-images">
                    <div class="thumbnail active">
                        <img src="${product.image}" alt="${product.name}">
                    </div>
                    <!-- Additional thumbnails would be added here in a real product -->
                </div>
            </div>
            <div class="product-info">
                <h1 class="product-title">${product.name}</h1>
                <div class="product-meta">
                    <div class="product-rating">
                        ${generateRatingStars(product.rating)}
                        <span class="count">${product.reviewCount} Reviews</span>
                    </div>
                    <div class="product-category">Category: <span>${product.category}</span></div>
                </div>
                <div class="product-price">
                    <span class="current-price">Ksh ${(product.price * 100).toFixed(0)}</span>
                    ${product.oldPrice ? `<span class="old-price">Ksh ${(product.oldPrice * 100).toFixed(0)}</span>` : ''}
                    ${product.oldPrice ? `<span class="discount-badge">-${Math.round((1 - product.price / product.oldPrice) * 100)}%</span>` : ''}
                </div>
                <div class="product-description">
                    <p>${product.description}</p>
                </div>
                <div class="product-actions">
                    <div class="quantity-control">
                        <button class="quantity-btn decrease">-</button>
                        <input type="number" value="1" min="1" id="product-quantity">
                        <button class="quantity-btn increase">+</button>
                    </div>
                    <button class="btn add-to-cart-btn" data-product-id="${product.id}">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                    <button class="btn-outline wishlist-btn" data-product-id="${product.id}">
                        <i class="far fa-heart"></i> Add to Wishlist
                    </button>
                </div>
                <div class="product-meta-info">
                    <div class="meta-item">
                        <i class="fas fa-truck"></i>
                        <span>Free shipping on orders over Ksh 10,000</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-undo"></i>
                        <span>30-day return policy</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure checkout</span>
                    </div>
                </div>
                <div class="social-share">
                    <span>Share:</span>
                    <a href="#" class="facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="pinterest"><i class="fab fa-pinterest"></i></a>
                    <a href="#" class="instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
        <div class="product-tabs">
            <div class="tabs-header">
                <button class="tab-btn active" data-tab="description">Description</button>
                <button class="tab-btn" data-tab="specifications">Specifications</button>
                <button class="tab-btn" data-tab="reviews">Reviews (${product.reviewCount})</button>
            </div>
            <div class="tabs-content">
                <div class="tab-panel active" id="description-panel">
                    <p>${product.description}</p>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
                </div>
                <div class="tab-panel" id="specifications-panel">
                    <table class="specs-table">
                        <tr>
                            <th>Material</th>
                            <td>Premium Quality</td>
                        </tr>
                        <tr>
                            <th>Dimensions</th>
                            <td>10 x 5 x 2 inches</td>
                        </tr>
                        <tr>
                            <th>Weight</th>
                            <td>0.5 kg</td>
                        </tr>
                        <tr>
                            <th>Color</th>
                            <td>Multiple options available</td>
                        </tr>
                        <tr>
                            <th>Warranty</th>
                            <td>1 year</td>
                        </tr>
                    </table>
                </div>
                <div class="tab-panel" id="reviews-panel">
                    <div class="reviews-summary">
                        <div class="average-rating">
                            <div class="rating-number">${product.rating.toFixed(1)}</div>
                            <div class="rating-stars">
                                ${generateRatingStars(product.rating)}
                            </div>
                            <div class="rating-count">${product.reviewCount} reviews</div>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-bar">
                                <span class="rating-label">5 stars</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: 75%"></div>
                                </div>
                                <span class="rating-percent">75%</span>
                            </div>
                            <div class="rating-bar">
                                <span class="rating-label">4 stars</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: 20%"></div>
                                </div>
                                <span class="rating-percent">20%</span>
                            </div>
                            <div class="rating-bar">
                                <span class="rating-label">3 stars</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: 5%"></div>
                                </div>
                                <span class="rating-percent">5%</span>
                            </div>
                            <div class="rating-bar">
                                <span class="rating-label">2 stars</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: 0%"></div>
                                </div>
                                <span class="rating-percent">0%</span>
                            </div>
                            <div class="rating-bar">
                                <span class="rating-label">1 star</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: 0%"></div>
                                </div>
                                <span class="rating-percent">0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="reviews-list">
                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Reviewer">
                                    </div>
                                    <div class="reviewer-details">
                                        <h4>Jane Smith</h4>
                                        <div class="review-date">June 15, 2023</div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    ${generateRatingStars(5)}
                                </div>
                            </div>
                            <div class="review-content">
                                <p>Absolutely love this product! The quality is exceptional and it exceeded my expectations. Would definitely recommend to anyone looking for a reliable and stylish option.</p>
                            </div>
                        </div>
                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Reviewer">
                                    </div>
                                    <div class="reviewer-details">
                                        <h4>John Doe</h4>
                                        <div class="review-date">May 28, 2023</div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    ${generateRatingStars(4)}
                                </div>
                            </div>
                            <div class="review-content">
                                <p>Great product overall. The only reason I'm giving it 4 stars instead of 5 is because the delivery took a bit longer than expected. Otherwise, very satisfied with my purchase.</p>
                            </div>
                        </div>
                    </div>
                    <div class="write-review">
                        <h3>Write a Review</h3>
                        <form class="review-form">
                            <div class="form-group">
                                <label>Your Rating</label>
                                <div class="rating-select">
                                    <i class="far fa-star" data-rating="1"></i>
                                    <i class="far fa-star" data-rating="2"></i>
                                    <i class="far fa-star" data-rating="3"></i>
                                    <i class="far fa-star" data-rating="4"></i>
                                    <i class="far fa-star" data-rating="5"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="review-title">Review Title</label>
                                <input type="text" id="review-title" placeholder="Give your review a title">
                            </div>
                            <div class="form-group">
                                <label for="review-content">Review</label>
                                <textarea id="review-content" rows="5" placeholder="Write your review here"></textarea>
                            </div>
                            <button type="submit" class="btn">Submit Review</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Display related products
    displayRelatedProducts(product);

    // Add event listeners for product detail page
    setupProductDetailEventListeners(product);
});

// Function to display related products
function displayRelatedProducts(currentProduct) {
    const relatedProductsContainer = document.getElementById('related-products-container');
    if (!relatedProductsContainer) return;

    // Get products from the same category, excluding the current product
    const relatedProducts = products
        .filter(product => product.category === currentProduct.category && product.id !== currentProduct.id)
        .slice(0, 4);

    relatedProductsContainer.innerHTML = relatedProducts.map(product => createProductCard(product)).join('');

    // Add event listeners to the add to cart buttons
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = parseInt(this.getAttribute('data-product-id'));
            addToCart(productId, 1);
        });
    });
}

// Function to set up event listeners for the product detail page
function setupProductDetailEventListeners(product) {
    // Quantity controls
    const quantityInput = document.getElementById('product-quantity');
    const decreaseBtn = document.querySelector('.quantity-btn.decrease');
    const increaseBtn = document.querySelector('.quantity-btn.increase');

    if (quantityInput && decreaseBtn && increaseBtn) {
        decreaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });

        increaseBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            quantityInput.value = currentValue + 1;
        });

        quantityInput.addEventListener('change', function() {
            const value = parseInt(this.value);
            if (isNaN(value) || value < 1) {
                this.value = 1;
            }
        });
    }

    // Add to cart button
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-product-id'));
            const quantity = parseInt(quantityInput.value);
            addToCart(productId, quantity);
        });
    }

    // Wishlist button
    const wishlistBtn = document.querySelector('.wishlist-btn');
    if (wishlistBtn) {
        wishlistBtn.addEventListener('click', function() {
            // In a real application, you would add this to a wishlist
            showNotification('Added to wishlist!', 'success');
            this.innerHTML = '<i class="fas fa-heart"></i> Added to Wishlist';
            this.disabled = true;
        });
    }

    // Product tabs
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));

            // Add active class to current button and panel
            this.classList.add('active');
            document.getElementById(`${tabId}-panel`).classList.add('active');
        });
    });

    // Rating selection in review form
    const ratingStars = document.querySelectorAll('.rating-select i');

    ratingStars.forEach(star => {
        star.addEventListener('mouseover', function() {
            const rating = parseInt(this.getAttribute('data-rating'));

            ratingStars.forEach(s => {
                const starRating = parseInt(s.getAttribute('data-rating'));
                if (starRating <= rating) {
                    s.classList.remove('far');
                    s.classList.add('fas');
                } else {
                    s.classList.remove('fas');
                    s.classList.add('far');
                }
            });
        });

        star.addEventListener('mouseout', function() {
            const selectedRating = document.querySelector('.rating-select').getAttribute('data-selected-rating');

            if (!selectedRating) {
                ratingStars.forEach(s => {
                    s.classList.remove('fas');
                    s.classList.add('far');
                });
            } else {
                ratingStars.forEach(s => {
                    const starRating = parseInt(s.getAttribute('data-rating'));
                    if (starRating <= parseInt(selectedRating)) {
                        s.classList.remove('far');
                        s.classList.add('fas');
                    } else {
                        s.classList.remove('fas');
                        s.classList.add('far');
                    }
                });
            }
        });

        star.addEventListener('click', function() {
            const rating = parseInt(this.getAttribute('data-rating'));
            document.querySelector('.rating-select').setAttribute('data-selected-rating', rating);

            ratingStars.forEach(s => {
                const starRating = parseInt(s.getAttribute('data-rating'));
                if (starRating <= rating) {
                    s.classList.remove('far');
                    s.classList.add('fas');
                } else {
                    s.classList.remove('fas');
                    s.classList.add('far');
                }
            });
        });
    });

    // Review form submission
    const reviewForm = document.querySelector('.review-form');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const selectedRating = document.querySelector('.rating-select').getAttribute('data-selected-rating');
            const reviewTitle = document.getElementById('review-title').value;
            const reviewContent = document.getElementById('review-content').value;

            if (!selectedRating) {
                showNotification('Please select a rating', 'error');
                return;
            }

            if (!reviewTitle || !reviewContent) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            // In a real application, you would send this to your server
            console.log('Review submitted:', {
                productId: product.id,
                rating: selectedRating,
                title: reviewTitle,
                content: reviewContent
            });

            showNotification('Thank you for your review!', 'success');

            // Reset form
            document.querySelector('.rating-select').removeAttribute('data-selected-rating');
            ratingStars.forEach(s => {
                s.classList.remove('fas');
                s.classList.add('far');
            });
            reviewForm.reset();
        });
    }
}
